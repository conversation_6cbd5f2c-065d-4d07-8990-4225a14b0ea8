import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '@prisma/client';
import { PrismaService } from '../../prisma/prisma.service';

interface RequestWithUser extends Request {
  user: {
    id: string;
    email: string;
  };
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private prismaService: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const roles = this.reflector.get<Role[]>('roles', context.getHandler());

    if (!roles?.length) return true;

    const { user } = context.switchToHttp().getRequest<RequestWithUser>();

    if (!user?.id) return false;

    // Get user's account to check role
    const account = await this.prismaService.account.findUnique({
      where: { userId: user.id },
      select: { role: true },
    });

    if (!account?.role) return false;

    return roles.includes(account.role);
  }
}
