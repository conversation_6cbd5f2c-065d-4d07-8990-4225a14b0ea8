import { ForbiddenException, Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AuthDto } from '../account/dto';
import * as argon from 'argon2';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthResponse } from './auth.types';

@Injectable()
export class AuthService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly config: ConfigService,
    private readonly jwt: JwtService,
  ) {}

  async signup(dto: AuthDto): Promise<AuthResponse> {
    try {
      const hashedPassword = await argon.hash(dto.password);

      // Create user and account in a transaction
      const result = await this.prisma.$transaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: {
            email: dto.email,
            password: hashedPassword,
          },
        });

        // Create account for the user
        const account = await tx.account.create({
          data: {
            userId: user.id,
            name: dto.email.split('@')[0], // Use email prefix as default name
            role: 'USER',
          },
        });

        return { user, account };
      });

      const token = await this.signToken(result.user.id, result.user.email);

      return {
        access_token: token,
        user: {
          id: result.user.id,
          email: result.user.email,
        },
      };
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        if (error.code === 'P2002')
          throw new ForbiddenException('An account with this email exists!');
      }

      throw error;
    }
  }

  async signin(dto: AuthDto): Promise<AuthResponse> {
    const user = await this.prisma.user.findUnique({
      where: { email: dto.email },
    });

    if (!user) {
      throw new ForbiddenException('Credentials incorrect');
    }

    const pwMatches = await argon.verify(user.password, dto.password);
    if (!pwMatches) {
      throw new ForbiddenException('Credentials incorrect');
    }

    const token = await this.signToken(user.id, user.email);

    return {
      access_token: token,
      user: {
        id: user.id,
        email: user.email,
      },
    };
  }

  async signToken(id: string, email: string): Promise<string> {
    const payload = { sub: id, email };

    const token = await this.jwt.signAsync(payload, {
      expiresIn: '7d', // Increased from 15m for better UX
      secret: this.config.get('JWT_SECRET'),
    });

    return token;
  }
}
